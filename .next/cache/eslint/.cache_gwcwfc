[{"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/layout.tsx": "1", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/page.tsx": "2", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/Dashboard.tsx": "3", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/DataVisualization.tsx": "4", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/FirestoreDataTable.tsx": "5", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/RealtimeDataTable.tsx": "6", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/lib/firebase.ts": "7"}, {"size": 689, "mtime": 1748770543736, "results": "8", "hashOfConfig": "9"}, {"size": 108, "mtime": 1748770725154, "results": "10", "hashOfConfig": "9"}, {"size": 5829, "mtime": 1748770768278, "results": "11", "hashOfConfig": "9"}, {"size": 9571, "mtime": 1748771335109, "results": "12", "hashOfConfig": "9"}, {"size": 9246, "mtime": 1748770843219, "results": "13", "hashOfConfig": "9"}, {"size": 5583, "mtime": 1748770800461, "results": "14", "hashOfConfig": "9"}, {"size": 1215, "mtime": 1748770738868, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ddln0", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/layout.tsx", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/page.tsx", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/Dashboard.tsx", ["37"], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/DataVisualization.tsx", ["38"], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/FirestoreDataTable.tsx", ["39"], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/RealtimeDataTable.tsx", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/lib/firebase.ts", [], [], {"ruleId": "40", "severity": 1, "message": "41", "line": 66, "column": 18, "nodeType": null, "messageId": "42", "endLine": 66, "endColumn": 21}, {"ruleId": "40", "severity": 1, "message": "43", "line": 24, "column": 43, "nodeType": null, "messageId": "42", "endLine": 24, "endColumn": 47}, {"ruleId": "40", "severity": 1, "message": "44", "line": 30, "column": 36, "nodeType": null, "messageId": "42", "endLine": 30, "endColumn": 39}, "@typescript-eslint/no-unused-vars", "'err' is defined but never used.", "unusedVar", "'path' is assigned a value but never used.", "'key' is defined but never used."]