[{"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/layout.tsx": "1", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/page.tsx": "2", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/Dashboard.tsx": "3", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/DataVisualization.tsx": "4", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/FirestoreDataTable.tsx": "5", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/RealtimeDataTable.tsx": "6", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/lib/firebase.ts": "7", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/firestore/collections/route.ts": "8", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/firestore/route.ts": "9", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/realtime/route.ts": "10", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/status/route.ts": "11", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/GoogleCloudStatus.tsx": "12", "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/lib/firebase-admin.ts": "13"}, {"size": 689, "mtime": 1748770543736, "results": "14", "hashOfConfig": "15"}, {"size": 108, "mtime": 1748770725154, "results": "16", "hashOfConfig": "15"}, {"size": 6092, "mtime": 1748772148349, "results": "17", "hashOfConfig": "15"}, {"size": 9571, "mtime": 1748771335109, "results": "18", "hashOfConfig": "15"}, {"size": 9246, "mtime": 1748770843219, "results": "19", "hashOfConfig": "15"}, {"size": 5583, "mtime": 1748770800461, "results": "20", "hashOfConfig": "15"}, {"size": 1215, "mtime": 1748770738868, "results": "21", "hashOfConfig": "15"}, {"size": 1415, "mtime": 1748772430601, "results": "22", "hashOfConfig": "15"}, {"size": 1897, "mtime": 1748772457407, "results": "23", "hashOfConfig": "15"}, {"size": 2373, "mtime": 1748772375355, "results": "24", "hashOfConfig": "15"}, {"size": 1849, "mtime": 1748772400268, "results": "25", "hashOfConfig": "15"}, {"size": 6784, "mtime": 1748772103525, "results": "26", "hashOfConfig": "15"}, {"size": 2723, "mtime": 1748772301513, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12vufzk", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/layout.tsx", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/page.tsx", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/Dashboard.tsx", ["67"], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/DataVisualization.tsx", ["68"], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/FirestoreDataTable.tsx", ["69"], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/RealtimeDataTable.tsx", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/lib/firebase.ts", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/firestore/collections/route.ts", ["70"], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/firestore/route.ts", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/realtime/route.ts", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/status/route.ts", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/GoogleCloudStatus.tsx", [], [], "/Users/<USER>/DevOps/firebase/cloud-cntxt/src/lib/firebase-admin.ts", [], [], {"ruleId": "71", "severity": 1, "message": "72", "line": 67, "column": 18, "nodeType": null, "messageId": "73", "endLine": 67, "endColumn": 21}, {"ruleId": "71", "severity": 1, "message": "74", "line": 24, "column": 43, "nodeType": null, "messageId": "73", "endLine": 24, "endColumn": 47}, {"ruleId": "71", "severity": 1, "message": "75", "line": 30, "column": 36, "nodeType": null, "messageId": "73", "endLine": 30, "endColumn": 39}, {"ruleId": "71", "severity": 1, "message": "76", "line": 14, "column": 17, "nodeType": null, "messageId": "73", "endLine": 14, "endColumn": 25}, "@typescript-eslint/no-unused-vars", "'err' is defined but never used.", "unusedVar", "'path' is assigned a value but never used.", "'key' is defined but never used.", "'snapshot' is assigned a value but never used."]