(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{52553:(e,t,a)=>{"use strict";a.d(t,{default:()=>P});var r=a(95155),s=a(12115),l=a(23915),d=a(81115),n=a(35317),o=a(49509);let i={apiKey:o.env.NEXT_PUBLIC_FIREBASE_API_KEY||"your-api-key",authDomain:o.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN||"your-project.firebaseapp.com",databaseURL:o.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL||"https://your-project-default-rtdb.firebaseio.com/",projectId:o.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID||"your-project-id",storageBucket:o.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET||"your-project.appspot.com",messagingSenderId:o.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID||"123456789",appId:o.env.NEXT_PUBLIC_FIREBASE_APP_ID||"1:123456789:web:abcdef123456"},c=(0,l.Wp)(i),x=(0,d.C3)(c),m=(0,n.aU)(c);var h=a(54213),g=a(57434),u=a(72713),b=a(85339),y=a(53904),p=a(5196),j=a(24357),N=a(66474),k=a(13052);function v(e){let{data:t,path:a,level:l}=e,[d,n]=(0,s.useState)(l<2),[o,i]=(0,s.useState)(!1),c=async e=>{try{await navigator.clipboard.writeText(e),i(!0),setTimeout(()=>i(!1),2e3)}catch(e){console.error("Failed to copy:",e)}};if(null==t)return(0,r.jsx)("div",{className:"ml-".concat(4*l," py-1 text-gray-500 dark:text-gray-400"),children:(0,r.jsxs)("span",{className:"font-mono text-sm",children:[a,": null"]})});if("object"!=typeof t)return(0,r.jsxs)("div",{className:"ml-".concat(4*l," py-1 flex items-center gap-2 group"),children:[(0,r.jsxs)("span",{className:"font-mono text-sm text-gray-700 dark:text-gray-300",children:[(0,r.jsxs)("span",{className:"text-blue-600 dark:text-blue-400",children:[a,":"]})," ",(0,r.jsx)("span",{className:"text-green-600 dark:text-green-400",children:"string"==typeof t?'"'.concat(t,'"'):String(t)})]}),(0,r.jsx)("button",{onClick:()=>c(String(t)),className:"opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-opacity",children:o?(0,r.jsx)(p.A,{className:"h-3 w-3 text-green-500"}):(0,r.jsx)(j.A,{className:"h-3 w-3 text-gray-400"})})]});let x=Object.keys(t),m=x.length>0;return(0,r.jsxs)("div",{className:"ml-".concat(4*l),children:[(0,r.jsxs)("div",{className:"py-1 flex items-center gap-1",children:[m&&(0,r.jsx)("button",{onClick:()=>n(!d),className:"p-0.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded",children:d?(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}):(0,r.jsx)(k.A,{className:"h-4 w-4 text-gray-500"})}),(0,r.jsxs)("span",{className:"font-mono text-sm font-medium text-gray-800 dark:text-gray-200",children:[a," ",m&&"(".concat(x.length," items)")]})]}),d&&m&&(0,r.jsx)("div",{className:"border-l border-gray-200 dark:border-gray-700 ml-2",children:x.map(e=>(0,r.jsx)(v,{data:t[e],path:e,level:l+1},e))})]})}function f(e){let{data:t,loading:a}=e;return a?(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading realtime database..."})]}):t&&0!==Object.keys(t).length?(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-4 flex items-center justify-between",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Realtime Database Content"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Object.keys(t).length," root nodes"]})]}),(0,r.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 max-h-96 overflow-auto",children:(0,r.jsx)("div",{className:"font-mono text-sm",children:Object.keys(t).map(e=>(0,r.jsx)(v,{data:t[e],path:e,level:0},e))})}),(0,r.jsx)("div",{className:"mt-4 text-xs text-gray-500 dark:text-gray-400",children:"\uD83D\uDCA1 Click the expand/collapse buttons to navigate through nested data. Hover over values to copy them to clipboard."})]}):(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:(0,r.jsx)("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No Data Found"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Your Firebase Realtime Database appears to be empty or you may need to configure your database rules."}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,r.jsx)("p",{className:"font-medium mb-2",children:"To add data to your Realtime Database:"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-left",children:[(0,r.jsx)("li",{children:"Go to your Firebase Console"}),(0,r.jsx)("li",{children:"Navigate to Realtime Database"}),(0,r.jsx)("li",{children:"Add some sample data"}),(0,r.jsx)("li",{children:"Refresh this dashboard"})]})]})]})}var w=a(47924);function D(e){let{document:t,isExpanded:a,onToggle:l}=e,[d,n]=(0,s.useState)(!1),o=async e=>{try{await navigator.clipboard.writeText(e),n(!0),setTimeout(()=>n(!1),2e3)}catch(e){console.error("Failed to copy:",e)}},i=(e,t)=>null==e?(0,r.jsx)("span",{className:"text-gray-500 italic",children:"null"}):"object"==typeof e?e.toDate&&"function"==typeof e.toDate?(0,r.jsx)("span",{className:"text-purple-600 dark:text-purple-400",children:e.toDate().toLocaleString()}):(0,r.jsx)("span",{className:"text-orange-600 dark:text-orange-400",children:JSON.stringify(e)}):"string"==typeof e?(0,r.jsxs)("span",{className:"text-green-600 dark:text-green-400",children:['"',e,'"']}):"number"==typeof e?(0,r.jsx)("span",{className:"text-blue-600 dark:text-blue-400",children:e}):"boolean"==typeof e?(0,r.jsx)("span",{className:"text-red-600 dark:text-red-400",children:String(e)}):(0,r.jsx)("span",{children:String(e)});return(0,r.jsxs)("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg mb-2",children:[(0,r.jsxs)("div",{className:"p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer flex items-center justify-between hover:bg-gray-100 dark:hover:bg-gray-700",onClick:l,children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[a?(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}):(0,r.jsx)(k.A,{className:"h-4 w-4 text-gray-500"}),(0,r.jsxs)("span",{className:"font-mono text-sm font-medium text-gray-800 dark:text-gray-200",children:["ID: ",t.id]})]}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),o(JSON.stringify(t,null,2))},className:"p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors",children:d?(0,r.jsx)(p.A,{className:"h-4 w-4 text-green-500"}):(0,r.jsx)(j.A,{className:"h-4 w-4 text-gray-400"})})]}),a&&(0,r.jsx)("div",{className:"p-4 space-y-2",children:Object.entries(t).map(e=>{let[t,a]=e;return"id"!==t&&(0,r.jsxs)("div",{className:"flex items-start gap-2 group",children:[(0,r.jsxs)("span",{className:"font-mono text-sm text-blue-600 dark:text-blue-400 min-w-0 flex-shrink-0",children:[t,":"]}),(0,r.jsx)("span",{className:"font-mono text-sm flex-1 min-w-0 break-all",children:i(a,t)}),(0,r.jsx)("button",{onClick:()=>o(String(a)),className:"opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-opacity flex-shrink-0",children:(0,r.jsx)(j.A,{className:"h-3 w-3 text-gray-400"})})]},t)})})]})}function S(e){let{data:t,loading:a}=e,[l,d]=(0,s.useState)(new Set),[n,o]=(0,s.useState)(""),[i,c]=(0,s.useState)(""),x=e=>{let t=new Set(l);t.has(e)?t.delete(e):t.add(e),d(t)};if(a)return(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading Firestore collections..."})]});let m=Object.keys(t);if(0===m.length)return(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:(0,r.jsx)("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No Collections Found"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"No Firestore collections were found. This could be because:"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-left",children:[(0,r.jsx)("li",{children:"Your Firestore database is empty"}),(0,r.jsx)("li",{children:"The collection names in the code don't match your actual collections"}),(0,r.jsx)("li",{children:"You need to update Firestore security rules"}),(0,r.jsx)("li",{children:"The collections are not accessible with current permissions"})]}),(0,r.jsxs)("p",{className:"mt-3 font-medium",children:["Update the collection names in ",(0,r.jsx)("code",{className:"bg-gray-200 dark:bg-gray-600 px-1 rounded",children:"src/components/Dashboard.tsx"})]})]})]});let h=i||m[0],g=t[h]||[],u=g.filter(e=>""===n||JSON.stringify(e).toLowerCase().includes(n.toLowerCase()));return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Firestore Collections"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:m.map(e=>(0,r.jsxs)("button",{onClick:()=>c(e),className:"px-3 py-1 rounded-full text-sm font-medium transition-colors ".concat(h===e?"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"),children:[e," (",t[e].length,")"]},e))}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(w.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search documents...",value:n,onChange:e=>o(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:[h," Collection"]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[u.length," of ",g.length," documents"]})]}),0===u.length?(0,r.jsx)("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:n?"No documents match your search.":"No documents in this collection."}):(0,r.jsx)("div",{className:"max-h-96 overflow-auto",children:u.map(e=>(0,r.jsx)(D,{document:e,isExpanded:l.has(e.id),onToggle:()=>x(e.id)},e.id))})]}),(0,r.jsx)("div",{className:"mt-4 text-xs text-gray-500 dark:text-gray-400",children:"\uD83D\uDCA1 Click on document headers to expand/collapse. Use the copy buttons to copy values to clipboard."})]})}var C=a(83540),F=a(3401),E=a(94754),A=a(96025),_=a(16238),R=a(94517),B=a(83394),I=a(8782),T=a(34e3),L=a(54811),O=a(93504),U=a(21374);let z=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82CA9D"];function M(e){let{realtimeData:t,firestoreData:a,loading:l}=e,d=(0,s.useMemo)(()=>{let e=Object.entries(a).map(e=>{let[t,a]=e;return{name:t,count:a.length,type:"Firestore"}}),r=function(e){return(arguments.length>1&&void 0!==arguments[1]&&arguments[1],e&&"object"==typeof e)?Object.keys(e).length+Object.values(e).reduce((e,t)=>"object"==typeof t&&null!==t?e+r(t):e,0):0},s=r(t);return s>0&&e.push({name:"Realtime DB",count:s,type:"Realtime"}),e},[t,a]),n=(0,s.useMemo)(()=>{let e=Object.values(a).reduce((e,t)=>e+t.length,0),r=Object.keys(t).length,s=[];return e>0&&s.push({name:"Firestore Documents",value:e}),r>0&&s.push({name:"Realtime DB Nodes",value:r}),s},[t,a]),o=(0,s.useMemo)(()=>{let e=new Date,t=[];for(let a=6;a>=0;a--){let r=new Date(e);r.setDate(r.getDate()-a),t.push({date:r.toLocaleDateString(),firestore:Math.floor(100*Math.random())+50,realtime:Math.floor(50*Math.random())+20})}return t},[]);return l?(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Preparing visualizations..."})]}):0===d.length&&0===n.length?(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 dark:text-gray-500 mb-4",children:(0,r.jsx)("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No Data to Visualize"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Add some data to your Firebase databases to see visualizations here."})]}):(0,r.jsxs)("div",{className:"p-6 space-y-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:"Data Visualizations"}),d.length>0&&(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Collection & Database Sizes"}),(0,r.jsx)(C.u,{width:"100%",height:300,children:(0,r.jsxs)(F.E,{data:d,children:[(0,r.jsx)(E.d,{strokeDasharray:"3 3",className:"opacity-30"}),(0,r.jsx)(A.W,{dataKey:"name",className:"text-gray-600 dark:text-gray-400",tick:{fontSize:12}}),(0,r.jsx)(_.h,{className:"text-gray-600 dark:text-gray-400",tick:{fontSize:12}}),(0,r.jsx)(R.m,{contentStyle:{backgroundColor:"rgb(31 41 55)",border:"none",borderRadius:"8px",color:"white"}}),(0,r.jsx)(B.y,{dataKey:"count",fill:"#3B82F6",radius:[4,4,0,0]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[n.length>0&&(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Data Distribution"}),(0,r.jsx)(C.u,{width:"100%",height:250,children:(0,r.jsxs)(I.r,{children:[(0,r.jsx)(T.F,{data:n,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:t,percent:a}=e;return"".concat(t," ").concat((100*a).toFixed(0),"%")},outerRadius:80,fill:"#8884d8",dataKey:"value",children:n.map((e,t)=>(0,r.jsx)(L.f,{fill:z[t%z.length]},"cell-".concat(t)))}),(0,r.jsx)(R.m,{contentStyle:{backgroundColor:"rgb(31 41 55)",border:"none",borderRadius:"8px",color:"white"}})]})})]}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Activity Trend (Sample Data)"}),(0,r.jsx)(C.u,{width:"100%",height:250,children:(0,r.jsxs)(O.b,{data:o,children:[(0,r.jsx)(E.d,{strokeDasharray:"3 3",className:"opacity-30"}),(0,r.jsx)(A.W,{dataKey:"date",className:"text-gray-600 dark:text-gray-400",tick:{fontSize:10}}),(0,r.jsx)(_.h,{className:"text-gray-600 dark:text-gray-400",tick:{fontSize:12}}),(0,r.jsx)(R.m,{contentStyle:{backgroundColor:"rgb(31 41 55)",border:"none",borderRadius:"8px",color:"white"}}),(0,r.jsx)(U.N,{type:"monotone",dataKey:"firestore",stroke:"#3B82F6",strokeWidth:2,name:"Firestore"}),(0,r.jsx)(U.N,{type:"monotone",dataKey:"realtime",stroke:"#10B981",strokeWidth:2,name:"Realtime DB"})]})}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:"* This is sample data. Replace with actual timestamp data from your database."})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:Object.keys(a).length}),(0,r.jsx)("div",{className:"text-sm text-blue-800 dark:text-blue-300",children:"Firestore Collections"})]}),(0,r.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:Object.values(a).reduce((e,t)=>e+t.length,0)}),(0,r.jsx)("div",{className:"text-sm text-green-800 dark:text-green-300",children:"Total Documents"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:Object.keys(t).length}),(0,r.jsx)("div",{className:"text-sm text-purple-800 dark:text-purple-300",children:"Realtime DB Nodes"})]})]})]})}function P(){let[e,t]=(0,s.useState)({}),[a,l]=(0,s.useState)({}),[o,i]=(0,s.useState)(!0),[c,p]=(0,s.useState)(null),[j,N]=(0,s.useState)("realtime");(0,s.useEffect)(()=>{let e=(0,d.KR)(x),a=(0,d.Zy)(e,e=>{try{let a=e.val();t(a||{}),p(null)}catch(e){console.error("Error fetching realtime data:",e),p("Failed to fetch realtime database data")}},e=>{console.error("Realtime database error:",e),p("Failed to connect to realtime database")});return()=>(0,d.AU)(e,"value",a)},[]);let k=async()=>{try{let e={};for(let t of["users","posts","products","orders"])try{let a=(await (0,n.GG)((0,n.rJ)(m,t))).docs.map(e=>({id:e.id,...e.data()}));a.length>0&&(e[t]=a)}catch(e){console.log("Collection ".concat(t," not found or inaccessible"))}l(e),p(null)}catch(e){console.error("Error fetching Firestore data:",e),p("Failed to fetch Firestore data")}finally{i(!1)}};(0,s.useEffect)(()=>{k()},[]);let v=[{id:"realtime",label:"Realtime Database",icon:h.A},{id:"firestore",label:"Firestore",icon:g.A},{id:"charts",label:"Visualizations",icon:u.A}];return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Firebase Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Monitor and visualize your Firebase database content"})]}),c&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center gap-2",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 text-red-600 dark:text-red-400"}),(0,r.jsx)("span",{className:"text-red-800 dark:text-red-200",children:c})]}),(0,r.jsxs)("div",{className:"mb-6 flex justify-between items-center",children:[(0,r.jsx)("div",{className:"flex space-x-1 bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm",children:v.map(e=>{let t=e.icon;return(0,r.jsxs)("button",{onClick:()=>N(e.id),className:"flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat(j===e.id?"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"),children:[(0,r.jsx)(t,{className:"h-4 w-4"}),e.label]},e.id)})}),(0,r.jsxs)("button",{onClick:()=>{i(!0),k()},disabled:o,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 ".concat(o?"animate-spin":"")}),"Refresh"]})]}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm",children:["realtime"===j&&(0,r.jsx)(f,{data:e,loading:o}),"firestore"===j&&(0,r.jsx)(S,{data:a,loading:o}),"charts"===j&&(0,r.jsx)(M,{realtimeData:e,firestoreData:a,loading:o})]})]})})}},77085:(e,t,a)=>{Promise.resolve().then(a.bind(a,52553))}},e=>{var t=t=>e(e.s=t);e.O(0,[992,507,51,441,684,358],()=>t(77085)),_N_E=e.O()}]);