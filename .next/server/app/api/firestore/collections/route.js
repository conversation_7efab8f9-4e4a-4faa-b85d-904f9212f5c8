(()=>{var e={};e.id=795,e.ids=[795],e.modules={588:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Pt:()=>u,iY:()=>l});var n=r(9801),i=r(7879),o=r(35567),a=e([n,i,o]);[n,i,o]=a.then?(await a)():a;let p=null,d=null,f=null;function c(){return p||(p=function(){if((0,n.getApps)().length>0)return(0,n.getApps)()[0];try{let e;if(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64){let t=process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64,r=Buffer.from(t,"base64").toString("utf8");e=JSON.parse(r)}else if(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)try{let t=r(29021).readFileSync(process.env.FIREBASE_SERVICE_ACCOUNT_KEY,"utf8");e=JSON.parse(t)}catch(e){console.warn("Failed to load service account from file:",e)}else try{let t=r(29021).readFileSync("./firebase-service-account.json","utf8");e=JSON.parse(t)}catch{console.warn("No service account found. Using default credentials.")}let t={databaseURL:process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL};return e&&(t.credential=(0,n.cert)(e)),(0,n.initializeApp)(t)}catch(e){throw console.error("Error initializing Firebase Admin:",e),e}}()),p}function l(){return d||(d=(0,i.getFirestore)(c())),d}function u(){return f||(f=(0,o.getDatabase)(c())),f}s()}catch(e){s(e)}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7879:e=>{"use strict";e.exports=import("firebase-admin/firestore")},9801:e=>{"use strict";e.exports=import("firebase-admin/app")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21111:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var n=r(96559),i=r(48088),o=r(37719),a=r(22653),c=e([a]);a=(c.then?(await c)():c)[0];let u=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/firestore/collections/route",pathname:"/api/firestore/collections",filename:"route",bundlePath:"app/api/firestore/collections/route"},resolvedPagePath:"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/firestore/collections/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:f}=u;function l(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}s()}catch(e){s(e)}})},22653:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>a});var n=r(32190),i=r(588),o=e([i]);async function a(){try{let e=(0,i.iY)(),t=await e.listCollections(),r=await Promise.all(t.map(async e=>{try{await e.limit(1).get();let t=await e.count().get();return{id:e.id,name:e.id,path:e.path,documentCount:t.data().count}}catch(t){return console.warn(`Error getting info for collection ${e.id}:`,t),{id:e.id,name:e.id,path:e.path,documentCount:0}}}));return n.NextResponse.json({collections:r,totalCollections:r.length})}catch(e){return console.error("Error listing collections:",e),n.NextResponse.json({error:"Failed to list collections",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}i=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35567:e=>{"use strict";e.exports=import("firebase-admin/database")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(21111));module.exports=s})();