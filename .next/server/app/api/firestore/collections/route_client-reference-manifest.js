globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/firestore/collections/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"26614":{"*":{"id":"35656","name":"*","chunks":[],"async":false}},"31295":{"*":{"id":"97173","name":"*","chunks":[],"async":false}},"46975":{"*":{"id":"40099","name":"*","chunks":[],"async":false}},"59665":{"*":{"id":"62763","name":"*","chunks":[],"async":false}},"63224":{"*":{"id":"88030","name":"*","chunks":[],"async":false}},"74911":{"*":{"id":"28827","name":"*","chunks":[],"async":false}},"87555":{"*":{"id":"38243","name":"*","chunks":[],"async":false}},"90894":{"*":{"id":"86346","name":"*","chunks":[],"async":false}},"94970":{"*":{"id":"27924","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/client/components/client-page.js":{"id":90894,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/esm/client/components/client-page.js":{"id":90894,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/client/components/client-segment.js":{"id":94970,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/esm/client/components/client-segment.js":{"id":94970,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/client/components/error-boundary.js":{"id":26614,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":26614,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":46975,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":46975,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/client/components/layout-router.js":{"id":87555,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/esm/client/components/layout-router.js":{"id":87555,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":74911,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":74911,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":59665,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":59665,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/client/components/render-from-template-context.js":{"id":31295,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":31295,"name":"*","chunks":[],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":4147,"name":"*","chunks":["177","static/chunks/app/layout-4a0e077f6bf143e8.js"],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":38489,"name":"*","chunks":["177","static/chunks/app/layout-4a0e077f6bf143e8.js"],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/globals.css":{"id":30347,"name":"*","chunks":["177","static/chunks/app/layout-4a0e077f6bf143e8.js"],"async":false},"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/Dashboard.tsx":{"id":63224,"name":"*","chunks":["992","static/chunks/bc9e92e6-83608b50ad764d9e.js","507","static/chunks/457b8330-52de1c49b7de3f32.js","944","static/chunks/944-b98da56ce4b27cf4.js","974","static/chunks/app/page-316fd168b070dd48.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/":[],"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/layout":[{"inlined":false,"path":"static/css/aec1b744ace3f0a0.css"}],"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/page":[],"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/firestore/route":[],"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/firestore/collections/route":[]},"rscModuleMapping":{"26614":{"*":{"id":"88170","name":"*","chunks":[],"async":false}},"30347":{"*":{"id":"61135","name":"*","chunks":[],"async":false}},"31295":{"*":{"id":"31307","name":"*","chunks":[],"async":false}},"46975":{"*":{"id":"49477","name":"*","chunks":[],"async":false}},"59665":{"*":{"id":"46577","name":"*","chunks":[],"async":false}},"63224":{"*":{"id":"71208","name":"*","chunks":[],"async":false}},"74911":{"*":{"id":"12089","name":"*","chunks":[],"async":false}},"87555":{"*":{"id":"29345","name":"*","chunks":[],"async":false}},"90894":{"*":{"id":"16444","name":"*","chunks":[],"async":false}},"94970":{"*":{"id":"16042","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}