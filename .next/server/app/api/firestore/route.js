(()=>{var e={};e.id=241,e.ids=[241],e.modules={588:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.d(r,{Pt:()=>p,iY:()=>u});var n=t(9801),a=t(7879),o=t(35567),i=e([n,a,o]);[n,a,o]=i.then?(await i)():i;let d=null,l=null,f=null;function c(){return d||(d=function(){if((0,n.getApps)().length>0)return(0,n.getApps)()[0];try{let e;if(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64){let r=process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64,t=Buffer.from(r,"base64").toString("utf8");e=JSON.parse(t)}else if(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)try{let r=t(29021).readFileSync(process.env.FIREBASE_SERVICE_ACCOUNT_KEY,"utf8");e=JSON.parse(r)}catch(e){console.warn("Failed to load service account from file:",e)}else try{let r=t(29021).readFileSync("./firebase-service-account.json","utf8");e=JSON.parse(r)}catch{console.warn("No service account found. Using default credentials.")}let r={databaseURL:process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL};return e&&(r.credential=(0,n.cert)(e)),(0,n.initializeApp)(r)}catch(e){throw console.error("Error initializing Firebase Admin:",e),e}}()),d}function u(){return l||(l=(0,a.getFirestore)(c())),l}function p(){return f||(f=(0,o.getDatabase)(c())),f}s()}catch(e){s(e)}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7879:e=>{"use strict";e.exports=import("firebase-admin/firestore")},9801:e=>{"use strict";e.exports=import("firebase-admin/app")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35567:e=>{"use strict";e.exports=import("firebase-admin/database")},42951:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{GET:()=>i,POST:()=>c});var n=t(32190),a=t(588),o=e([a]);async function i(e){try{let{searchParams:r}=new URL(e.url),t=r.get("collection"),s=parseInt(r.get("limit")||"100");if(!t)return n.NextResponse.json({error:"Collection parameter is required"},{status:400});let o=(0,a.iY)(),i=(await o.collection(t).limit(s).get()).docs.map(e=>({id:e.id,...e.data()}));return n.NextResponse.json({collection:t,count:i.length,documents:i})}catch(e){return console.error("Error fetching Firestore data:",e),n.NextResponse.json({error:"Failed to fetch Firestore data",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function c(e){try{let{collection:r,data:t}=await e.json();if(!r||!t)return n.NextResponse.json({error:"Collection and data are required"},{status:400});let s=(0,a.iY)(),o=await s.collection(r).add(t);return n.NextResponse.json({success:!0,id:o.id,message:`Document added to ${r}`})}catch(e){return console.error("Error adding document:",e),n.NextResponse.json({error:"Failed to add document",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}a=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},92703:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{patchFetch:()=>u,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var n=t(96559),a=t(48088),o=t(37719),i=t(42951),c=e([i]);i=(c.then?(await c)():c)[0];let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/firestore/route",pathname:"/api/firestore",filename:"route",bundlePath:"app/api/firestore/route"},resolvedPagePath:"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/firestore/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:f}=p;function u(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(92703));module.exports=s})();