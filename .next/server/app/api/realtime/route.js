(()=>{var e={};e.id=305,e.ids=[305],e.modules={588:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Pt:()=>p,iY:()=>c});var a=r(9801),n=r(7879),i=r(35567),o=e([a,n,i]);[a,n,i]=o.then?(await o)():o;let l=null,d=null,f=null;function u(){return l||(l=function(){if((0,a.getApps)().length>0)return(0,a.getApps)()[0];try{let e;if(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64){let t=process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64,r=Buffer.from(t,"base64").toString("utf8");e=JSON.parse(r)}else if(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)try{let t=r(29021).readFileSync(process.env.FIREBASE_SERVICE_ACCOUNT_KEY,"utf8");e=JSON.parse(t)}catch(e){console.warn("Failed to load service account from file:",e)}else try{let t=r(29021).readFileSync("./firebase-service-account.json","utf8");e=JSON.parse(t)}catch{console.warn("No service account found. Using default credentials.")}let t={databaseURL:process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL};return e&&(t.credential=(0,a.cert)(e)),(0,a.initializeApp)(t)}catch(e){throw console.error("Error initializing Firebase Admin:",e),e}}()),l}function c(){return d||(d=(0,n.getFirestore)(u())),d}function p(){return f||(f=(0,i.getDatabase)(u())),f}s()}catch(e){s(e)}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7879:e=>{"use strict";e.exports=import("firebase-admin/firestore")},9801:e=>{"use strict";e.exports=import("firebase-admin/app")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35567:e=>{"use strict";e.exports=import("firebase-admin/database")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74285:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>o,POST:()=>u});var a=r(32190),n=r(588),i=e([n]);async function o(e){try{let{searchParams:t}=new URL(e.url),r=t.get("path")||"/",s="true"===t.get("shallow"),i=(0,n.Pt)().ref(r),o=await i.once("value",void 0,s),u=o.val();return a.NextResponse.json({path:r,data:u,exists:o.exists(),hasChildren:o.hasChildren(),numChildren:o.numChildren()})}catch(e){return console.error("Error fetching Realtime Database data:",e),a.NextResponse.json({error:"Failed to fetch Realtime Database data",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function u(e){try{let t,{path:r,data:s,method:i="set"}=await e.json();if(!r||void 0===s)return a.NextResponse.json({error:"Path and data are required"},{status:400});let o=(0,n.Pt)().ref(r);switch(i){case"set":await o.set(s),t={message:`Data set at ${r}`};break;case"update":await o.update(s),t={message:`Data updated at ${r}`};break;case"push":let u=await o.push(s);t={message:`Data pushed to ${r}`,key:u.key};break;default:return a.NextResponse.json({error:"Invalid method. Use set, update, or push"},{status:400})}return a.NextResponse.json({success:!0,...t})}catch(e){return console.error("Error writing to Realtime Database:",e),a.NextResponse.json({error:"Failed to write to Realtime Database",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}n=(i.then?(await i)():i)[0],s()}catch(e){s(e)}})},78335:()=>{},86771:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var a=r(96559),n=r(48088),i=r(37719),o=r(74285),u=e([o]);o=(u.then?(await u)():u)[0];let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/realtime/route",pathname:"/api/realtime",filename:"route",bundlePath:"app/api/realtime/route"},resolvedPagePath:"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/realtime/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:f}=p;function c(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}s()}catch(e){s(e)}})},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(86771));module.exports=s})();