(()=>{var e={};e.id=144,e.ids=[144],e.modules={588:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Pt:()=>p,iY:()=>u});var n=r(9801),a=r(7879),i=r(35567),o=e([n,a,i]);[n,a,i]=o.then?(await o)():o;let l=null,d=null,E=null;function c(){return l||(l=function(){if((0,n.getApps)().length>0)return(0,n.getApps)()[0];try{let e;if(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64){let t=process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64,r=Buffer.from(t,"base64").toString("utf8");e=JSON.parse(r)}else if(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)try{let t=r(29021).readFileSync(process.env.FIREBASE_SERVICE_ACCOUNT_KEY,"utf8");e=JSON.parse(t)}catch(e){console.warn("Failed to load service account from file:",e)}else try{let t=r(29021).readFileSync("./firebase-service-account.json","utf8");e=JSON.parse(t)}catch{console.warn("No service account found. Using default credentials.")}let t={databaseURL:process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL};return e&&(t.credential=(0,n.cert)(e)),(0,n.initializeApp)(t)}catch(e){throw console.error("Error initializing Firebase Admin:",e),e}}()),l}function u(){return d||(d=(0,a.getFirestore)(c())),d}function p(){return E||(E=(0,i.getDatabase)(c())),E}s()}catch(e){s(e)}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7879:e=>{"use strict";e.exports=import("firebase-admin/firestore")},9801:e=>{"use strict";e.exports=import("firebase-admin/app")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35567:e=>{"use strict";e.exports=import("firebase-admin/database")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},58526:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>o});var n=r(32190),a=r(588),i=e([a]);async function o(){let e={timestamp:new Date().toISOString(),firebase:{admin:!1,firestore:!1,realtimeDb:!1},googleCloud:{projectId:process.env.GOOGLE_CLOUD_PROJECT_ID||process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,serviceAccount:!1},environment:{nodeEnv:"production",hasServiceAccountKey:!!(process.env.FIREBASE_SERVICE_ACCOUNT_KEY||process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64)}};try{e.firebase.admin=!0;try{let t=(0,a.iY)();await t.collection("_test_connection").limit(1).get(),e.firebase.firestore=!0}catch(e){console.warn("Firestore connection test failed:",e)}try{let t=(0,a.Pt)();await t.ref("/.info/connected").once("value"),e.firebase.realtimeDb=!0}catch(e){console.warn("Realtime Database connection test failed:",e)}e.googleCloud.serviceAccount=e.environment.hasServiceAccountKey}catch(t){return console.error("Status check error:",t),n.NextResponse.json({...e,error:t instanceof Error?t.message:"Unknown error"},{status:500})}let t=e.firebase.admin&&(e.firebase.firestore||e.firebase.realtimeDb);return n.NextResponse.json(e,{status:t?200:503})}a=(i.then?(await i)():i)[0],s()}catch(e){s(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},95659:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>p,serverHooks:()=>E,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var n=r(96559),a=r(48088),i=r(37719),o=r(58526),c=e([o]);o=(c.then?(await c)():c)[0];let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/status/route",pathname:"/api/status",filename:"route",bundlePath:"app/api/status/route"},resolvedPagePath:"/Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/status/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:E}=p;function u(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}s()}catch(e){s(e)}})},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(95659));module.exports=s})();