{"version": 3, "sources": [], "sections": [{"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/DevOps/firebase/cloud-cntxt/src/lib/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getDatabase } from 'firebase/database';\nimport { getFirestore } from 'firebase/firestore';\n\n// Your Firebase configuration\n// Replace these values with your actual Firebase project configuration\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || \"your-api-key\",\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || \"your-project.firebaseapp.com\",\n  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL || \"https://your-project-default-rtdb.firebaseio.com/\",\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || \"your-project-id\",\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || \"your-project.appspot.com\",\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || \"123456789\",\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || \"1:123456789:web:abcdef123456\"\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Realtime Database and get a reference to the service\nexport const database = getDatabase(app);\n\n// Initialize Cloud Firestore and get a reference to the service\nexport const firestore = getFirestore(app);\n\nexport default app;\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEA,8BAA8B;AAC9B,uEAAuE;AACvE,MAAM,iBAAiB;IACrB,QAAQ,QAAQ,GAAG,CAAC,4BAA4B,IAAI;IACpD,YAAY,QAAQ,GAAG,CAAC,gCAAgC,IAAI;IAC5D,aAAa,QAAQ,GAAG,CAAC,iCAAiC,IAAI;IAC9D,WAAW,QAAQ,GAAG,CAAC,+BAA+B,IAAI;IAC1D,eAAe,QAAQ,GAAG,CAAC,mCAAmC,IAAI;IAClE,mBAAmB,QAAQ,GAAG,CAAC,wCAAwC,IAAI;IAC3E,OAAO,QAAQ,GAAG,CAAC,2BAA2B,IAAI;AACpD;AAEA,sBAAsB;AACtB,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;AAGnB,MAAM,WAAW,CAAA,GAAA,qLAAA,CAAA,cAAW,AAAD,EAAE;AAG7B,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;uCAEvB", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/RealtimeDataTable.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ChevronDown, ChevronRight, Co<PERSON>, Check } from 'lucide-react';\n\ninterface RealtimeDataTableProps {\n  data: any;\n  loading: boolean;\n}\n\ninterface TreeNodeProps {\n  data: any;\n  path: string;\n  level: number;\n}\n\nfunction TreeNode({ data, path, level }: TreeNodeProps) {\n  const [isExpanded, setIsExpanded] = useState(level < 2);\n  const [copied, setCopied] = useState(false);\n\n  const copyToClipboard = async (value: string) => {\n    try {\n      await navigator.clipboard.writeText(value);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy:', err);\n    }\n  };\n\n  if (data === null || data === undefined) {\n    return (\n      <div className={`ml-${level * 4} py-1 text-gray-500 dark:text-gray-400`}>\n        <span className=\"font-mono text-sm\">{path}: null</span>\n      </div>\n    );\n  }\n\n  if (typeof data !== 'object') {\n    return (\n      <div className={`ml-${level * 4} py-1 flex items-center gap-2 group`}>\n        <span className=\"font-mono text-sm text-gray-700 dark:text-gray-300\">\n          <span className=\"text-blue-600 dark:text-blue-400\">{path}:</span>{' '}\n          <span className=\"text-green-600 dark:text-green-400\">\n            {typeof data === 'string' ? `\"${data}\"` : String(data)}\n          </span>\n        </span>\n        <button\n          onClick={() => copyToClipboard(String(data))}\n          className=\"opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-opacity\"\n        >\n          {copied ? (\n            <Check className=\"h-3 w-3 text-green-500\" />\n          ) : (\n            <Copy className=\"h-3 w-3 text-gray-400\" />\n          )}\n        </button>\n      </div>\n    );\n  }\n\n  const keys = Object.keys(data);\n  const hasChildren = keys.length > 0;\n\n  return (\n    <div className={`ml-${level * 4}`}>\n      <div className=\"py-1 flex items-center gap-1\">\n        {hasChildren && (\n          <button\n            onClick={() => setIsExpanded(!isExpanded)}\n            className=\"p-0.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded\"\n          >\n            {isExpanded ? (\n              <ChevronDown className=\"h-4 w-4 text-gray-500\" />\n            ) : (\n              <ChevronRight className=\"h-4 w-4 text-gray-500\" />\n            )}\n          </button>\n        )}\n        <span className=\"font-mono text-sm font-medium text-gray-800 dark:text-gray-200\">\n          {path} {hasChildren && `(${keys.length} items)`}\n        </span>\n      </div>\n      \n      {isExpanded && hasChildren && (\n        <div className=\"border-l border-gray-200 dark:border-gray-700 ml-2\">\n          {keys.map((key) => (\n            <TreeNode\n              key={key}\n              data={data[key]}\n              path={key}\n              level={level + 1}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default function RealtimeDataTable({ data, loading }: RealtimeDataTableProps) {\n  if (loading) {\n    return (\n      <div className=\"p-8 text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600 dark:text-gray-400\">Loading realtime database...</p>\n      </div>\n    );\n  }\n\n  if (!data || Object.keys(data).length === 0) {\n    return (\n      <div className=\"p-8 text-center\">\n        <div className=\"text-gray-400 dark:text-gray-500 mb-4\">\n          <svg className=\"mx-auto h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3\" />\n          </svg>\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n          No Data Found\n        </h3>\n        <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n          Your Firebase Realtime Database appears to be empty or you may need to configure your database rules.\n        </p>\n        <div className=\"text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n          <p className=\"font-medium mb-2\">To add data to your Realtime Database:</p>\n          <ol className=\"list-decimal list-inside space-y-1 text-left\">\n            <li>Go to your Firebase Console</li>\n            <li>Navigate to Realtime Database</li>\n            <li>Add some sample data</li>\n            <li>Refresh this dashboard</li>\n          </ol>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-4 flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          Realtime Database Content\n        </h2>\n        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {Object.keys(data).length} root nodes\n        </div>\n      </div>\n      \n      <div className=\"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 max-h-96 overflow-auto\">\n        <div className=\"font-mono text-sm\">\n          {Object.keys(data).map((key) => (\n            <TreeNode\n              key={key}\n              data={data[key]}\n              path={key}\n              level={0}\n            />\n          ))}\n        </div>\n      </div>\n      \n      <div className=\"mt-4 text-xs text-gray-500 dark:text-gray-400\">\n        💡 Click the expand/collapse buttons to navigate through nested data. \n        Hover over values to copy them to clipboard.\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAgBA,SAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAiB;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,qBACE,8OAAC;YAAI,WAAW,CAAC,GAAG,EAAE,QAAQ,EAAE,sCAAsC,CAAC;sBACrE,cAAA,8OAAC;gBAAK,WAAU;;oBAAqB;oBAAK;;;;;;;;;;;;IAGhD;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,qBACE,8OAAC;YAAI,WAAW,CAAC,GAAG,EAAE,QAAQ,EAAE,mCAAmC,CAAC;;8BAClE,8OAAC;oBAAK,WAAU;;sCACd,8OAAC;4BAAK,WAAU;;gCAAoC;gCAAK;;;;;;;wBAAS;sCAClE,8OAAC;4BAAK,WAAU;sCACb,OAAO,SAAS,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO;;;;;;;;;;;;8BAGrD,8OAAC;oBACC,SAAS,IAAM,gBAAgB,OAAO;oBACtC,WAAU;8BAET,uBACC,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;6CAEjB,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;;IAK1B;IAEA,MAAM,OAAO,OAAO,IAAI,CAAC;IACzB,MAAM,cAAc,KAAK,MAAM,GAAG;IAElC,qBACE,8OAAC;QAAI,WAAW,CAAC,GAAG,EAAE,QAAQ,GAAG;;0BAC/B,8OAAC;gBAAI,WAAU;;oBACZ,6BACC,8OAAC;wBACC,SAAS,IAAM,cAAc,CAAC;wBAC9B,WAAU;kCAET,2BACC,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;iDAEvB,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAI9B,8OAAC;wBAAK,WAAU;;4BACb;4BAAK;4BAAE,eAAe,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;YAIlD,cAAc,6BACb,8OAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;wBAEC,MAAM,IAAI,CAAC,IAAI;wBACf,MAAM;wBACN,OAAO,QAAQ;uBAHV;;;;;;;;;;;;;;;;AAUnB;AAEe,SAAS,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAA0B;IACjF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;IAGtD;IAEA,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,MAAM,MAAM,KAAK,GAAG;QAC3C,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAoB,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACxE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,8OAAC;oBAAG,WAAU;8BAAyD;;;;;;8BAGvE,8OAAC;oBAAE,WAAU;8BAAwC;;;;;;8BAGrD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAmB;;;;;;sCAChC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;IAKd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCAGpE,8OAAC;wBAAI,WAAU;;4BACZ,OAAO,IAAI,CAAC,MAAM,MAAM;4BAAC;;;;;;;;;;;;;0BAI9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,oBACtB,8OAAC;4BAEC,MAAM,IAAI,CAAC,IAAI;4BACf,MAAM;4BACN,OAAO;2BAHF;;;;;;;;;;;;;;;0BASb,8OAAC;gBAAI,WAAU;0BAAgD;;;;;;;;;;;;AAMrE", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/FirestoreDataTable.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ChevronDown, ChevronRight, Copy, Check, Search } from 'lucide-react';\n\ninterface FirestoreDataTableProps {\n  data: { [collection: string]: any[] };\n  loading: boolean;\n}\n\ninterface DocumentViewerProps {\n  document: any;\n  isExpanded: boolean;\n  onToggle: () => void;\n}\n\nfunction DocumentViewer({ document, isExpanded, onToggle }: DocumentViewerProps) {\n  const [copied, setCopied] = useState(false);\n\n  const copyToClipboard = async (value: string) => {\n    try {\n      await navigator.clipboard.writeText(value);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy:', err);\n    }\n  };\n\n  const renderValue = (value: any, key: string) => {\n    if (value === null || value === undefined) {\n      return <span className=\"text-gray-500 italic\">null</span>;\n    }\n\n    if (typeof value === 'object') {\n      if (value.toDate && typeof value.toDate === 'function') {\n        // Firestore Timestamp\n        return <span className=\"text-purple-600 dark:text-purple-400\">{value.toDate().toLocaleString()}</span>;\n      }\n      return <span className=\"text-orange-600 dark:text-orange-400\">{JSON.stringify(value)}</span>;\n    }\n\n    if (typeof value === 'string') {\n      return <span className=\"text-green-600 dark:text-green-400\">\"{value}\"</span>;\n    }\n\n    if (typeof value === 'number') {\n      return <span className=\"text-blue-600 dark:text-blue-400\">{value}</span>;\n    }\n\n    if (typeof value === 'boolean') {\n      return <span className=\"text-red-600 dark:text-red-400\">{String(value)}</span>;\n    }\n\n    return <span>{String(value)}</span>;\n  };\n\n  return (\n    <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg mb-2\">\n      <div \n        className=\"p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer flex items-center justify-between hover:bg-gray-100 dark:hover:bg-gray-700\"\n        onClick={onToggle}\n      >\n        <div className=\"flex items-center gap-2\">\n          {isExpanded ? (\n            <ChevronDown className=\"h-4 w-4 text-gray-500\" />\n          ) : (\n            <ChevronRight className=\"h-4 w-4 text-gray-500\" />\n          )}\n          <span className=\"font-mono text-sm font-medium text-gray-800 dark:text-gray-200\">\n            ID: {document.id}\n          </span>\n        </div>\n        <button\n          onClick={(e) => {\n            e.stopPropagation();\n            copyToClipboard(JSON.stringify(document, null, 2));\n          }}\n          className=\"p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors\"\n        >\n          {copied ? (\n            <Check className=\"h-4 w-4 text-green-500\" />\n          ) : (\n            <Copy className=\"h-4 w-4 text-gray-400\" />\n          )}\n        </button>\n      </div>\n      \n      {isExpanded && (\n        <div className=\"p-4 space-y-2\">\n          {Object.entries(document).map(([key, value]) => (\n            key !== 'id' && (\n              <div key={key} className=\"flex items-start gap-2 group\">\n                <span className=\"font-mono text-sm text-blue-600 dark:text-blue-400 min-w-0 flex-shrink-0\">\n                  {key}:\n                </span>\n                <span className=\"font-mono text-sm flex-1 min-w-0 break-all\">\n                  {renderValue(value, key)}\n                </span>\n                <button\n                  onClick={() => copyToClipboard(String(value))}\n                  className=\"opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-opacity flex-shrink-0\"\n                >\n                  <Copy className=\"h-3 w-3 text-gray-400\" />\n                </button>\n              </div>\n            )\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default function FirestoreDataTable({ data, loading }: FirestoreDataTableProps) {\n  const [expandedDocs, setExpandedDocs] = useState<Set<string>>(new Set());\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCollection, setSelectedCollection] = useState<string>('');\n\n  const toggleDocument = (docId: string) => {\n    const newExpanded = new Set(expandedDocs);\n    if (newExpanded.has(docId)) {\n      newExpanded.delete(docId);\n    } else {\n      newExpanded.add(docId);\n    }\n    setExpandedDocs(newExpanded);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"p-8 text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600 dark:text-gray-400\">Loading Firestore collections...</p>\n      </div>\n    );\n  }\n\n  const collections = Object.keys(data);\n\n  if (collections.length === 0) {\n    return (\n      <div className=\"p-8 text-center\">\n        <div className=\"text-gray-400 dark:text-gray-500 mb-4\">\n          <svg className=\"mx-auto h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n          </svg>\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n          No Collections Found\n        </h3>\n        <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n          No Firestore collections were found. This could be because:\n        </p>\n        <div className=\"text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n          <ul className=\"list-disc list-inside space-y-1 text-left\">\n            <li>Your Firestore database is empty</li>\n            <li>The collection names in the code don't match your actual collections</li>\n            <li>You need to update Firestore security rules</li>\n            <li>The collections are not accessible with current permissions</li>\n          </ul>\n          <p className=\"mt-3 font-medium\">\n            Update the collection names in <code className=\"bg-gray-200 dark:bg-gray-600 px-1 rounded\">src/components/Dashboard.tsx</code>\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  const currentCollection = selectedCollection || collections[0];\n  const currentData = data[currentCollection] || [];\n\n  const filteredData = currentData.filter(doc => \n    searchTerm === '' || \n    JSON.stringify(doc).toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          Firestore Collections\n        </h2>\n        \n        {/* Collection Selector */}\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {collections.map((collection) => (\n            <button\n              key={collection}\n              onClick={() => setSelectedCollection(collection)}\n              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                currentCollection === collection\n                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'\n                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\n              }`}\n            >\n              {collection} ({data[collection].length})\n            </button>\n          ))}\n        </div>\n\n        {/* Search */}\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search documents...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n      </div>\n\n      {/* Documents */}\n      <div className=\"space-y-2\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n            {currentCollection} Collection\n          </h3>\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n            {filteredData.length} of {currentData.length} documents\n          </div>\n        </div>\n\n        {filteredData.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n            {searchTerm ? 'No documents match your search.' : 'No documents in this collection.'}\n          </div>\n        ) : (\n          <div className=\"max-h-96 overflow-auto\">\n            {filteredData.map((doc) => (\n              <DocumentViewer\n                key={doc.id}\n                document={doc}\n                isExpanded={expandedDocs.has(doc.id)}\n                onToggle={() => toggleDocument(doc.id)}\n              />\n            ))}\n          </div>\n        )}\n      </div>\n      \n      <div className=\"mt-4 text-xs text-gray-500 dark:text-gray-400\">\n        💡 Click on document headers to expand/collapse. Use the copy buttons to copy values to clipboard.\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAgBA,SAAS,eAAe,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAuB;IAC7E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,MAAM,cAAc,CAAC,OAAY;QAC/B,IAAI,UAAU,QAAQ,UAAU,WAAW;YACzC,qBAAO,8OAAC;gBAAK,WAAU;0BAAuB;;;;;;QAChD;QAEA,IAAI,OAAO,UAAU,UAAU;YAC7B,IAAI,MAAM,MAAM,IAAI,OAAO,MAAM,MAAM,KAAK,YAAY;gBACtD,sBAAsB;gBACtB,qBAAO,8OAAC;oBAAK,WAAU;8BAAwC,MAAM,MAAM,GAAG,cAAc;;;;;;YAC9F;YACA,qBAAO,8OAAC;gBAAK,WAAU;0BAAwC,KAAK,SAAS,CAAC;;;;;;QAChF;QAEA,IAAI,OAAO,UAAU,UAAU;YAC7B,qBAAO,8OAAC;gBAAK,WAAU;;oBAAqC;oBAAE;oBAAM;;;;;;;QACtE;QAEA,IAAI,OAAO,UAAU,UAAU;YAC7B,qBAAO,8OAAC;gBAAK,WAAU;0BAAoC;;;;;;QAC7D;QAEA,IAAI,OAAO,UAAU,WAAW;YAC9B,qBAAO,8OAAC;gBAAK,WAAU;0BAAkC,OAAO;;;;;;QAClE;QAEA,qBAAO,8OAAC;sBAAM,OAAO;;;;;;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,SAAS;;kCAET,8OAAC;wBAAI,WAAU;;4BACZ,2BACC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CAE1B,8OAAC;gCAAK,WAAU;;oCAAiE;oCAC1E,SAAS,EAAE;;;;;;;;;;;;;kCAGpB,8OAAC;wBACC,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB,gBAAgB,KAAK,SAAS,CAAC,UAAU,MAAM;wBACjD;wBACA,WAAU;kCAET,uBACC,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;iDAEjB,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAKrB,4BACC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GACzC,QAAQ,sBACN,8OAAC;wBAAc,WAAU;;0CACvB,8OAAC;gCAAK,WAAU;;oCACb;oCAAI;;;;;;;0CAEP,8OAAC;gCAAK,WAAU;0CACb,YAAY,OAAO;;;;;;0CAEtB,8OAAC;gCACC,SAAS,IAAM,gBAAgB,OAAO;gCACtC,WAAU;0CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;uBAXV;;;;;;;;;;;;;;;;AAoBxB;AAEe,SAAS,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAA2B;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErE,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,QAAQ;YAC1B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,gBAAgB;IAClB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;IAGtD;IAEA,MAAM,cAAc,OAAO,IAAI,CAAC;IAEhC,IAAI,YAAY,MAAM,KAAK,GAAG;QAC5B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAoB,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACxE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,8OAAC;oBAAG,WAAU;8BAAyD;;;;;;8BAGvE,8OAAC;oBAAE,WAAU;8BAAwC;;;;;;8BAGrD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;sCAEN,8OAAC;4BAAE,WAAU;;gCAAmB;8CACC,8OAAC;oCAAK,WAAU;8CAA4C;;;;;;;;;;;;;;;;;;;;;;;;IAKrG;IAEA,MAAM,oBAAoB,sBAAsB,WAAW,CAAC,EAAE;IAC9D,MAAM,cAAc,IAAI,CAAC,kBAAkB,IAAI,EAAE;IAEjD,MAAM,eAAe,YAAY,MAAM,CAAC,CAAA,MACtC,eAAe,MACf,KAAK,SAAS,CAAC,KAAK,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGnE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAKzE,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;gCAEC,SAAS,IAAM,sBAAsB;gCACrC,WAAW,CAAC,6DAA6D,EACvE,sBAAsB,aAClB,kEACA,0GACJ;;oCAED;oCAAW;oCAAG,IAAI,CAAC,WAAW,CAAC,MAAM;oCAAC;;+BARlC;;;;;;;;;;kCAcX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCACX;oCAAkB;;;;;;;0CAErB,8OAAC;gCAAI,WAAU;;oCACZ,aAAa,MAAM;oCAAC;oCAAK,YAAY,MAAM;oCAAC;;;;;;;;;;;;;oBAIhD,aAAa,MAAM,KAAK,kBACvB,8OAAC;wBAAI,WAAU;kCACZ,aAAa,oCAAoC;;;;;6CAGpD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;gCAEC,UAAU;gCACV,YAAY,aAAa,GAAG,CAAC,IAAI,EAAE;gCACnC,UAAU,IAAM,eAAe,IAAI,EAAE;+BAHhC,IAAI,EAAE;;;;;;;;;;;;;;;;0BAUrB,8OAAC;gBAAI,WAAU;0BAAgD;;;;;;;;;;;;AAKrE", "debugId": null}}, {"offset": {"line": 1106, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/DataVisualization.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { <PERSON>Chart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';\n\ninterface DataVisualizationProps {\n  realtimeData: any;\n  firestoreData: { [collection: string]: any[] };\n  loading: boolean;\n}\n\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];\n\nexport default function DataVisualization({ realtimeData, firestoreData, loading }: DataVisualizationProps) {\n  const chartData = useMemo(() => {\n    // Collection sizes for Firestore\n    const collectionSizes = Object.entries(firestoreData).map(([name, docs]) => ({\n      name,\n      count: docs.length,\n      type: 'Firestore'\n    }));\n\n    // Count items in realtime database\n    const countRealtimeItems = (obj: any, path = ''): number => {\n      if (!obj || typeof obj !== 'object') return 0;\n      return Object.keys(obj).length + Object.values(obj).reduce((sum: number, value: any) => {\n        if (typeof value === 'object' && value !== null) {\n          return sum + countRealtimeItems(value);\n        }\n        return sum;\n      }, 0);\n    };\n\n    const realtimeCount = countRealtimeItems(realtimeData);\n    \n    if (realtimeCount > 0) {\n      collectionSizes.push({\n        name: 'Realtime DB',\n        count: realtimeCount,\n        type: 'Realtime'\n      });\n    }\n\n    return collectionSizes;\n  }, [realtimeData, firestoreData]);\n\n  const typeDistribution = useMemo(() => {\n    const firestoreTotal = Object.values(firestoreData).reduce((sum, docs) => sum + docs.length, 0);\n    const realtimeTotal = Object.keys(realtimeData).length;\n\n    const distribution = [];\n    if (firestoreTotal > 0) {\n      distribution.push({ name: 'Firestore Documents', value: firestoreTotal });\n    }\n    if (realtimeTotal > 0) {\n      distribution.push({ name: 'Realtime DB Nodes', value: realtimeTotal });\n    }\n\n    return distribution;\n  }, [realtimeData, firestoreData]);\n\n  // Sample time-series data (you can replace this with actual timestamp data from your database)\n  const timeSeriesData = useMemo(() => {\n    const now = new Date();\n    const data = [];\n    \n    for (let i = 6; i >= 0; i--) {\n      const date = new Date(now);\n      date.setDate(date.getDate() - i);\n      \n      // This is mock data - replace with actual data from your database\n      data.push({\n        date: date.toLocaleDateString(),\n        firestore: Math.floor(Math.random() * 100) + 50,\n        realtime: Math.floor(Math.random() * 50) + 20,\n      });\n    }\n    \n    return data;\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"p-8 text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600 dark:text-gray-400\">Preparing visualizations...</p>\n      </div>\n    );\n  }\n\n  if (chartData.length === 0 && typeDistribution.length === 0) {\n    return (\n      <div className=\"p-8 text-center\">\n        <div className=\"text-gray-400 dark:text-gray-500 mb-4\">\n          <svg className=\"mx-auto h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n          </svg>\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n          No Data to Visualize\n        </h3>\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          Add some data to your Firebase databases to see visualizations here.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 space-y-8\">\n      <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n        Data Visualizations\n      </h2>\n\n      {/* Collection/Database Sizes Bar Chart */}\n      {chartData.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Collection & Database Sizes\n          </h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <BarChart data={chartData}>\n              <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n              <XAxis \n                dataKey=\"name\" \n                className=\"text-gray-600 dark:text-gray-400\"\n                tick={{ fontSize: 12 }}\n              />\n              <YAxis \n                className=\"text-gray-600 dark:text-gray-400\"\n                tick={{ fontSize: 12 }}\n              />\n              <Tooltip \n                contentStyle={{\n                  backgroundColor: 'rgb(31 41 55)',\n                  border: 'none',\n                  borderRadius: '8px',\n                  color: 'white'\n                }}\n              />\n              <Bar dataKey=\"count\" fill=\"#3B82F6\" radius={[4, 4, 0, 0]} />\n            </BarChart>\n          </ResponsiveContainer>\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Data Type Distribution Pie Chart */}\n        {typeDistribution.length > 0 && (\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n              Data Distribution\n            </h3>\n            <ResponsiveContainer width=\"100%\" height={250}>\n              <PieChart>\n                <Pie\n                  data={typeDistribution}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                >\n                  {typeDistribution.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                  ))}\n                </Pie>\n                <Tooltip \n                  contentStyle={{\n                    backgroundColor: 'rgb(31 41 55)',\n                    border: 'none',\n                    borderRadius: '8px',\n                    color: 'white'\n                  }}\n                />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n        )}\n\n        {/* Sample Time Series Chart */}\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Activity Trend (Sample Data)\n          </h3>\n          <ResponsiveContainer width=\"100%\" height={250}>\n            <LineChart data={timeSeriesData}>\n              <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n              <XAxis \n                dataKey=\"date\" \n                className=\"text-gray-600 dark:text-gray-400\"\n                tick={{ fontSize: 10 }}\n              />\n              <YAxis \n                className=\"text-gray-600 dark:text-gray-400\"\n                tick={{ fontSize: 12 }}\n              />\n              <Tooltip \n                contentStyle={{\n                  backgroundColor: 'rgb(31 41 55)',\n                  border: 'none',\n                  borderRadius: '8px',\n                  color: 'white'\n                }}\n              />\n              <Line \n                type=\"monotone\" \n                dataKey=\"firestore\" \n                stroke=\"#3B82F6\" \n                strokeWidth={2}\n                name=\"Firestore\"\n              />\n              <Line \n                type=\"monotone\" \n                dataKey=\"realtime\" \n                stroke=\"#10B981\" \n                strokeWidth={2}\n                name=\"Realtime DB\"\n              />\n            </LineChart>\n          </ResponsiveContainer>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-2\">\n            * This is sample data. Replace with actual timestamp data from your database.\n          </p>\n        </div>\n      </div>\n\n      {/* Summary Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800\">\n          <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n            {Object.keys(firestoreData).length}\n          </div>\n          <div className=\"text-sm text-blue-800 dark:text-blue-300\">Firestore Collections</div>\n        </div>\n        \n        <div className=\"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800\">\n          <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n            {Object.values(firestoreData).reduce((sum, docs) => sum + docs.length, 0)}\n          </div>\n          <div className=\"text-sm text-green-800 dark:text-green-300\">Total Documents</div>\n        </div>\n        \n        <div className=\"bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800\">\n          <div className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">\n            {Object.keys(realtimeData).length}\n          </div>\n          <div className=\"text-sm text-purple-800 dark:text-purple-300\">Realtime DB Nodes</div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAWA,MAAM,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU;AAElE,SAAS,kBAAkB,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,EAA0B;IACxG,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,iCAAiC;QACjC,MAAM,kBAAkB,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,GAAK,CAAC;gBAC3E;gBACA,OAAO,KAAK,MAAM;gBAClB,MAAM;YACR,CAAC;QAED,mCAAmC;QACnC,MAAM,qBAAqB,CAAC,KAAU,OAAO,EAAE;YAC7C,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU,OAAO;YAC5C,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,KAAa;gBACvE,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;oBAC/C,OAAO,MAAM,mBAAmB;gBAClC;gBACA,OAAO;YACT,GAAG;QACL;QAEA,MAAM,gBAAgB,mBAAmB;QAEzC,IAAI,gBAAgB,GAAG;YACrB,gBAAgB,IAAI,CAAC;gBACnB,MAAM;gBACN,OAAO;gBACP,MAAM;YACR;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAc;KAAc;IAEhC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,MAAM,iBAAiB,OAAO,MAAM,CAAC,eAAe,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;QAC7F,MAAM,gBAAgB,OAAO,IAAI,CAAC,cAAc,MAAM;QAEtD,MAAM,eAAe,EAAE;QACvB,IAAI,iBAAiB,GAAG;YACtB,aAAa,IAAI,CAAC;gBAAE,MAAM;gBAAuB,OAAO;YAAe;QACzE;QACA,IAAI,gBAAgB,GAAG;YACrB,aAAa,IAAI,CAAC;gBAAE,MAAM;gBAAqB,OAAO;YAAc;QACtE;QAEA,OAAO;IACT,GAAG;QAAC;QAAc;KAAc;IAEhC,+FAA+F;IAC/F,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,EAAE;QAEf,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YAC3B,MAAM,OAAO,IAAI,KAAK;YACtB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;YAE9B,kEAAkE;YAClE,KAAK,IAAI,CAAC;gBACR,MAAM,KAAK,kBAAkB;gBAC7B,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;gBAC7C,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;YAC7C;QACF;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;IAGtD;IAEA,IAAI,UAAU,MAAM,KAAK,KAAK,iBAAiB,MAAM,KAAK,GAAG;QAC3D,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAoB,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACxE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,8OAAC;oBAAG,WAAU;8BAAyD;;;;;;8BAGvE,8OAAC;oBAAE,WAAU;8BAAmC;;;;;;;;;;;;IAKtD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA2D;;;;;;YAKxE,UAAU,MAAM,GAAG,mBAClB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC,mKAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAO,QAAQ;kCACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;4BAAC,MAAM;;8CACd,8OAAC,6JAAA,CAAA,gBAAa;oCAAC,iBAAgB;oCAAM,WAAU;;;;;;8CAC/C,8OAAC,qJAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;oCACV,MAAM;wCAAE,UAAU;oCAAG;;;;;;8CAEvB,8OAAC,qJAAA,CAAA,QAAK;oCACJ,WAAU;oCACV,MAAM;wCAAE,UAAU;oCAAG;;;;;;8CAEvB,8OAAC,uJAAA,CAAA,UAAO;oCACN,cAAc;wCACZ,iBAAiB;wCACjB,QAAQ;wCACR,cAAc;wCACd,OAAO;oCACT;;;;;;8CAEF,8OAAC,mJAAA,CAAA,MAAG;oCAAC,SAAQ;oCAAQ,MAAK;oCAAU,QAAQ;wCAAC;wCAAG;wCAAG;wCAAG;qCAAE;;;;;;;;;;;;;;;;;;;;;;;0BAMhE,8OAAC;gBAAI,WAAU;;oBAEZ,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;sDACP,8OAAC,+IAAA,CAAA,MAAG;4CACF,MAAM;4CACN,IAAG;4CACH,IAAG;4CACH,WAAW;4CACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;4CACtE,aAAa;4CACb,MAAK;4CACL,SAAQ;sDAEP,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC,oJAAA,CAAA,OAAI;oDAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;mDAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;sDAG9B,8OAAC,uJAAA,CAAA,UAAO;4CACN,cAAc;gDACZ,iBAAiB;gDACjB,QAAQ;gDACR,cAAc;gDACd,OAAO;4CACT;;;;;;;;;;;;;;;;;;;;;;;kCAQV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;4CAAM,WAAU;;;;;;sDAC/C,8OAAC,qJAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;4CACV,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAEvB,8OAAC,qJAAA,CAAA,QAAK;4CACJ,WAAU;4CACV,MAAM;gDAAE,UAAU;4CAAG;;;;;;sDAEvB,8OAAC,uJAAA,CAAA,UAAO;4CACN,cAAc;gDACZ,iBAAiB;gDACjB,QAAQ;gDACR,cAAc;gDACd,OAAO;4CACT;;;;;;sDAEF,8OAAC,oJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,aAAa;4CACb,MAAK;;;;;;sDAEP,8OAAC,oJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,aAAa;4CACb,MAAK;;;;;;;;;;;;;;;;;0CAIX,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;;;;;;;0BAOjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,OAAO,IAAI,CAAC,eAAe,MAAM;;;;;;0CAEpC,8OAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAG5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,OAAO,MAAM,CAAC,eAAe,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;;;;;;0CAEzE,8OAAC;gCAAI,WAAU;0CAA6C;;;;;;;;;;;;kCAG9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,OAAO,IAAI,CAAC,cAAc,MAAM;;;;;;0CAEnC,8OAAC;gCAAI,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;;;;;;;AAKxE", "debugId": null}}, {"offset": {"line": 1660, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/DevOps/firebase/cloud-cntxt/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { database, firestore } from '@/lib/firebase';\nimport { ref, onValue, off } from 'firebase/database';\nimport { collection, getDocs } from 'firebase/firestore';\nimport { Database, FileText, BarChart3, RefreshCw, AlertCircle } from 'lucide-react';\nimport RealtimeDataTable from './RealtimeDataTable';\nimport FirestoreDataTable from './FirestoreDataTable';\nimport DataVisualization from './DataVisualization';\n\ninterface DatabaseData {\n  [key: string]: any;\n}\n\ninterface FirestoreData {\n  [collection: string]: any[];\n}\n\nexport default function Dashboard() {\n  const [realtimeData, setRealtimeData] = useState<DatabaseData>({});\n  const [firestoreData, setFirestoreData] = useState<FirestoreData>({});\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState<'realtime' | 'firestore' | 'charts'>('realtime');\n\n  // Fetch Realtime Database data\n  useEffect(() => {\n    const dbRef = ref(database);\n    \n    const unsubscribe = onValue(dbRef, (snapshot) => {\n      try {\n        const data = snapshot.val();\n        setRealtimeData(data || {});\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching realtime data:', err);\n        setError('Failed to fetch realtime database data');\n      }\n    }, (error) => {\n      console.error('Realtime database error:', error);\n      setError('Failed to connect to realtime database');\n    });\n\n    return () => off(dbRef, 'value', unsubscribe);\n  }, []);\n\n  // Fetch Firestore data\n  const fetchFirestoreData = async () => {\n    try {\n      // Note: This is a simplified approach. In a real app, you'd want to\n      // specify which collections to fetch or have a way to discover them\n      const collections = ['users', 'posts', 'products', 'orders']; // Add your collection names here\n      const firestoreResult: FirestoreData = {};\n\n      for (const collectionName of collections) {\n        try {\n          const querySnapshot = await getDocs(collection(firestore, collectionName));\n          const docs = querySnapshot.docs.map(doc => ({\n            id: doc.id,\n            ...doc.data()\n          }));\n          if (docs.length > 0) {\n            firestoreResult[collectionName] = docs;\n          }\n        } catch (err) {\n          console.log(`Collection ${collectionName} not found or inaccessible`);\n        }\n      }\n\n      setFirestoreData(firestoreResult);\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching Firestore data:', err);\n      setError('Failed to fetch Firestore data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchFirestoreData();\n  }, []);\n\n  const refreshData = () => {\n    setLoading(true);\n    fetchFirestoreData();\n  };\n\n  const tabs = [\n    { id: 'realtime', label: 'Realtime Database', icon: Database },\n    { id: 'firestore', label: 'Firestore', icon: FileText },\n    { id: 'charts', label: 'Visualizations', icon: BarChart3 },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n            Firebase Dashboard\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Monitor and visualize your Firebase database content\n          </p>\n        </div>\n\n        {/* Error Alert */}\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center gap-2\">\n            <AlertCircle className=\"h-5 w-5 text-red-600 dark:text-red-400\" />\n            <span className=\"text-red-800 dark:text-red-200\">{error}</span>\n          </div>\n        )}\n\n        {/* Controls */}\n        <div className=\"mb-6 flex justify-between items-center\">\n          <div className=\"flex space-x-1 bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                    activeTab === tab.id\n                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'\n                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  {tab.label}\n                </button>\n              );\n            })}\n          </div>\n\n          <button\n            onClick={refreshData}\n            disabled={loading}\n            className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors\"\n          >\n            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />\n            Refresh\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm\">\n          {activeTab === 'realtime' && (\n            <RealtimeDataTable data={realtimeData} loading={loading} />\n          )}\n          {activeTab === 'firestore' && (\n            <FirestoreDataTable data={firestoreData} loading={loading} />\n          )}\n          {activeTab === 'charts' && (\n            <DataVisualization \n              realtimeData={realtimeData} \n              firestoreData={firestoreData} \n              loading={loading} \n            />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;;AAmBe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,CAAC;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuC;IAEhF,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,CAAA,GAAA,qLAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,WAAQ;QAE1B,MAAM,cAAc,CAAA,GAAA,qLAAA,CAAA,UAAO,AAAD,EAAE,OAAO,CAAC;YAClC,IAAI;gBACF,MAAM,OAAO,SAAS,GAAG;gBACzB,gBAAgB,QAAQ,CAAC;gBACzB,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,SAAS;YACX;QACF,GAAG,CAAC;YACF,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX;QAEA,OAAO,IAAM,CAAA,GAAA,qLAAA,CAAA,MAAG,AAAD,EAAE,OAAO,SAAS;IACnC,GAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,IAAI;YACF,oEAAoE;YACpE,oEAAoE;YACpE,MAAM,cAAc;gBAAC;gBAAS;gBAAS;gBAAY;aAAS,EAAE,iCAAiC;YAC/F,MAAM,kBAAiC,CAAC;YAExC,KAAK,MAAM,kBAAkB,YAAa;gBACxC,IAAI;oBACF,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,YAAS,EAAE;oBAC1D,MAAM,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;4BAC1C,IAAI,IAAI,EAAE;4BACV,GAAG,IAAI,IAAI,EAAE;wBACf,CAAC;oBACD,IAAI,KAAK,MAAM,GAAG,GAAG;wBACnB,eAAe,CAAC,eAAe,GAAG;oBACpC;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,eAAe,0BAA0B,CAAC;gBACtE;YACF;YAEA,iBAAiB;YACjB,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,WAAW;QACX;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAqB,MAAM,0MAAA,CAAA,WAAQ;QAAC;QAC7D;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,8MAAA,CAAA,WAAQ;QAAC;QACtD;YAAE,IAAI;YAAU,OAAO;YAAkB,MAAM,kNAAA,CAAA,YAAS;QAAC;KAC1D;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;gBAMjD,uBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAkC;;;;;;;;;;;;8BAKtD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC;gCACT,MAAM,OAAO,IAAI,IAAI;gCACrB,qBACE,8OAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,mFAAmF,EAC7F,cAAc,IAAI,EAAE,GAChB,kEACA,8EACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;wCACf,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;4BAYjB;;;;;;sCAGF,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,iBAAiB,IAAI;;;;;;gCAAI;;;;;;;;;;;;;8BAMxE,8OAAC;oBAAI,WAAU;;wBACZ,cAAc,4BACb,8OAAC,uIAAA,CAAA,UAAiB;4BAAC,MAAM;4BAAc,SAAS;;;;;;wBAEjD,cAAc,6BACb,8OAAC,wIAAA,CAAA,UAAkB;4BAAC,MAAM;4BAAe,SAAS;;;;;;wBAEnD,cAAc,0BACb,8OAAC,uIAAA,CAAA,UAAiB;4BAChB,cAAc;4BACd,eAAe;4BACf,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAOvB", "debugId": null}}]}