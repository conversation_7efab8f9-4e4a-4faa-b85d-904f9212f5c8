{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/DevOps/firebase/cloud-cntxt/src/lib/firebase-admin.ts"], "sourcesContent": ["import { initializeApp, getApps, cert } from 'firebase-admin/app';\nimport { getFirestore } from 'firebase-admin/firestore';\nimport { getDatabase } from 'firebase-admin/database';\n\n// Initialize Firebase Admin SDK\nfunction initializeFirebaseAdmin() {\n  // Check if Firebase Admin is already initialized\n  if (getApps().length > 0) {\n    return getApps()[0];\n  }\n\n  try {\n    let serviceAccount;\n\n    // Try to get service account from environment variable (base64 encoded)\n    if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64) {\n      const serviceAccountBase64 = process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64;\n      const serviceAccountJson = Buffer.from(serviceAccountBase64, 'base64').toString('utf8');\n      serviceAccount = JSON.parse(serviceAccountJson);\n    } \n    // Try to get service account from file path\n    else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {\n      try {\n        const fs = require('fs');\n        const serviceAccountJson = fs.readFileSync(process.env.FIREBASE_SERVICE_ACCOUNT_KEY, 'utf8');\n        serviceAccount = JSON.parse(serviceAccountJson);\n      } catch (error) {\n        console.warn('Failed to load service account from file:', error);\n      }\n    }\n    // Try to load from default location\n    else {\n      try {\n        const fs = require('fs');\n        const serviceAccountJson = fs.readFileSync('./firebase-service-account.json', 'utf8');\n        serviceAccount = JSON.parse(serviceAccountJson);\n      } catch {\n        console.warn('No service account found. Using default credentials.');\n        // In production, you might want to use Application Default Credentials\n        // This works when running on Google Cloud Platform\n      }\n    }\n\n    const config: any = {\n      databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,\n    };\n\n    // Add credentials if service account is available\n    if (serviceAccount) {\n      config.credential = cert(serviceAccount);\n    }\n\n    return initializeApp(config);\n  } catch (error) {\n    console.error('Error initializing Firebase Admin:', error);\n    throw error;\n  }\n}\n\n// Lazy initialization to avoid build-time errors\nlet adminApp: any = null;\nlet adminDb: any = null;\nlet adminRealtimeDb: any = null;\n\nexport function getAdminApp() {\n  if (!adminApp) {\n    adminApp = initializeFirebaseAdmin();\n  }\n  return adminApp;\n}\n\nexport function getAdminDb() {\n  if (!adminDb) {\n    adminDb = getFirestore(getAdminApp());\n  }\n  return adminDb;\n}\n\nexport function getAdminRealtimeDb() {\n  if (!adminRealtimeDb) {\n    adminRealtimeDb = getDatabase(getAdminApp());\n  }\n  return adminRealtimeDb;\n}\n\n// For backward compatibility\nexport { getAdminDb as adminDb, getAdminRealtimeDb as adminRealtimeDb };\n\nexport default getAdminApp;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;;;;;;;AAEA,gCAAgC;AAChC,SAAS;IACP,iDAAiD;IACjD,IAAI,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,IAAI,MAAM,GAAG,GAAG;QACxB,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,GAAG,CAAC,EAAE;IACrB;IAEA,IAAI;QACF,IAAI;QAEJ,wEAAwE;QACxE,IAAI,QAAQ,GAAG,CAAC,mCAAmC,EAAE;YACnD,MAAM,uBAAuB,QAAQ,GAAG,CAAC,mCAAmC;YAC5E,MAAM,qBAAqB,OAAO,IAAI,CAAC,sBAAsB,UAAU,QAAQ,CAAC;YAChF,iBAAiB,KAAK,KAAK,CAAC;QAC9B,OAEK,IAAI,QAAQ,GAAG,CAAC,4BAA4B,EAAE;YACjD,IAAI;gBACF,MAAM;gBACN,MAAM,qBAAqB,GAAG,YAAY,CAAC,QAAQ,GAAG,CAAC,4BAA4B,EAAE;gBACrF,iBAAiB,KAAK,KAAK,CAAC;YAC9B,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,6CAA6C;YAC5D;QACF,OAEK;YACH,IAAI;gBACF,MAAM;gBACN,MAAM,qBAAqB,GAAG,YAAY,CAAC,mCAAmC;gBAC9E,iBAAiB,KAAK,KAAK,CAAC;YAC9B,EAAE,OAAM;gBACN,QAAQ,IAAI,CAAC;YACb,uEAAuE;YACvE,mDAAmD;YACrD;QACF;QAEA,MAAM,SAAc;YAClB,WAAW;QACb;QAEA,kDAAkD;QAClD,IAAI,gBAAgB;YAClB,OAAO,UAAU,GAAG,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE;QAC3B;QAEA,OAAO,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAEA,iDAAiD;AACjD,IAAI,WAAgB;AACpB,IAAI,UAAe;AACnB,IAAI,kBAAuB;AAEpB,SAAS;IACd,IAAI,CAAC,UAAU;QACb,WAAW;IACb;IACA,OAAO;AACT;AAEO,SAAS;IACd,IAAI,CAAC,SAAS;QACZ,UAAU,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD,EAAE;IACzB;IACA,OAAO;AACT;AAEO,SAAS;IACd,IAAI,CAAC,iBAAiB;QACpB,kBAAkB,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE;IAChC;IACA,OAAO;AACT;;uCAKe", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/DevOps/firebase/cloud-cntxt/src/app/api/status/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { getAdminDb, getAdminRealtimeDb } from '@/lib/firebase-admin';\n\nexport async function GET() {\n  const status = {\n    timestamp: new Date().toISOString(),\n    firebase: {\n      admin: false,\n      firestore: false,\n      realtimeDb: false\n    },\n    googleCloud: {\n      projectId: process.env.GOOGLE_CLOUD_PROJECT_ID || process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n      serviceAccount: false\n    },\n    environment: {\n      nodeEnv: process.env.NODE_ENV,\n      hasServiceAccountKey: !!(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64)\n    }\n  };\n\n  try {\n    // Test Firebase Admin initialization\n    status.firebase.admin = true;\n\n    // Test Firestore connection\n    try {\n      const adminDb = getAdminDb();\n      await adminDb.collection('_test_connection').limit(1).get();\n      status.firebase.firestore = true;\n    } catch (error) {\n      console.warn('Firestore connection test failed:', error);\n    }\n\n    // Test Realtime Database connection\n    try {\n      const adminRealtimeDb = getAdminRealtimeDb();\n      await adminRealtimeDb.ref('/.info/connected').once('value');\n      status.firebase.realtimeDb = true;\n    } catch (error) {\n      console.warn('Realtime Database connection test failed:', error);\n    }\n\n    // Check service account\n    status.googleCloud.serviceAccount = status.environment.hasServiceAccountKey;\n\n  } catch (error) {\n    console.error('Status check error:', error);\n    return NextResponse.json({\n      ...status,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n\n  const allGood = status.firebase.admin && \n                  (status.firebase.firestore || status.firebase.realtimeDb);\n\n  return NextResponse.json(status, { \n    status: allGood ? 200 : 503 \n  });\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;;;AAEO,eAAe;IACpB,MAAM,SAAS;QACb,WAAW,IAAI,OAAO,WAAW;QACjC,UAAU;YACR,OAAO;YACP,WAAW;YACX,YAAY;QACd;QACA,aAAa;YACX,WAAW,QAAQ,GAAG,CAAC,uBAAuB;YAC9C,gBAAgB;QAClB;QACA,aAAa;YACX,OAAO;YACP,sBAAsB,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,4BAA4B,IAAI,QAAQ,GAAG,CAAC,mCAAmC;QACtH;IACF;IAEA,IAAI;QACF,qCAAqC;QACrC,OAAO,QAAQ,CAAC,KAAK,GAAG;QAExB,4BAA4B;QAC5B,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;YACzB,MAAM,QAAQ,UAAU,CAAC,oBAAoB,KAAK,CAAC,GAAG,GAAG;YACzD,OAAO,QAAQ,CAAC,SAAS,GAAG;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,qCAAqC;QACpD;QAEA,oCAAoC;QACpC,IAAI;YACF,MAAM,kBAAkB,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD;YACzC,MAAM,gBAAgB,GAAG,CAAC,oBAAoB,IAAI,CAAC;YACnD,OAAO,QAAQ,CAAC,UAAU,GAAG;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,6CAA6C;QAC5D;QAEA,wBAAwB;QACxB,OAAO,WAAW,CAAC,cAAc,GAAG,OAAO,WAAW,CAAC,oBAAoB;IAE7E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,GAAG,MAAM;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;IAEA,MAAM,UAAU,OAAO,QAAQ,CAAC,KAAK,IACrB,CAAC,OAAO,QAAQ,CAAC,SAAS,IAAI,OAAO,QAAQ,CAAC,UAAU;IAExE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;QAC/B,QAAQ,UAAU,MAAM;IAC1B;AACF", "debugId": null}}]}