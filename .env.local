# Firebase Configuration (Client-side)
# Copy this file to .env.local and replace with your actual Firebase project values
# Get these values from your Firebase Console > Project Settings > General > Your apps

NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key-here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com/
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=*********
NEXT_PUBLIC_FIREBASE_APP_ID=1:*********:web:abcdef123456

# Google Cloud Configuration (Server-side)
# These are used for Firebase Admin SDK and server-side operations

GOOGLE_CLOUD_PROJECT_ID=your-project-id

# Option 1: Service Account Key File Path (Development)
# Place your firebase-service-account.json file in the project root
FIREBASE_SERVICE_ACCOUNT_KEY=./firebase-service-account.json

# Option 2: Service Account Key as Base64 (Production)
# Convert your JSON key to base64: base64 -i firebase-service-account.json
# FIREBASE_SERVICE_ACCOUNT_KEY_BASE64=your-base64-encoded-service-account-key

# Environment
NODE_ENV=development
