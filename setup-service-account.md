# Service Account Key Setup Guide

## Download Service Account Key from Google Cloud Console

### Step 1: Access Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your Firebase project from the project dropdown
3. Navigate to **IAM & Admin** > **Service Accounts**

### Step 2: Find Your Service Account
Look for your service account in the list. It might be named something like:
- `<EMAIL>`
- `<EMAIL>`

### Step 3: Download the Key
1. Click on your service account email
2. Go to the **Keys** tab
3. Click **Add Key** > **Create new key**
4. Select **JSON** format
5. Click **Create** - the file will download automatically

### Step 4: Place the Key File
1. Move the downloaded file to your project root
2. Rename it to `firebase-service-account.json`

```bash
# Example commands:
mv ~/Downloads/your-project-firebase-adminsdk-xxxxx.json ./firebase-service-account.json
```

## Verify Service Account Permissions

Your service account needs these roles:
- **Firebase Admin SDK Administrator Service Agent**
- **Cloud Datastore User** (for Firestore)
- **Firebase Realtime Database Admin**

To add roles:
1. In Google Cloud Console, go to **IAM & Admin** > **IAM**
2. Find your service account
3. Click the edit (pencil) icon
4. Add the required roles if missing

## Security Note
- Never commit this file to version control
- The file is already added to `.gitignore`
- For production, use environment variables instead
