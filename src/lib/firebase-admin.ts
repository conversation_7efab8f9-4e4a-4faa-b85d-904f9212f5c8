import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getDatabase } from 'firebase-admin/database';

// Initialize Firebase Admin SDK
function initializeFirebaseAdmin() {
  // Check if Firebase Admin is already initialized
  if (getApps().length > 0) {
    return getApps()[0];
  }

  try {
    let serviceAccount;

    // Try to get service account from environment variable (base64 encoded)
    if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64) {
      const serviceAccountBase64 = process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64;
      const serviceAccountJson = Buffer.from(serviceAccountBase64, 'base64').toString('utf8');
      serviceAccount = JSON.parse(serviceAccountJson);
    } 
    // Try to get service account from file path
    else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
      try {
        const fs = require('fs');
        const serviceAccountJson = fs.readFileSync(process.env.FIREBASE_SERVICE_ACCOUNT_KEY, 'utf8');
        serviceAccount = JSON.parse(serviceAccountJson);
      } catch (error) {
        console.warn('Failed to load service account from file:', error);
      }
    }
    // Try to load from default location
    else {
      try {
        const fs = require('fs');
        const serviceAccountJson = fs.readFileSync('./firebase-service-account.json', 'utf8');
        serviceAccount = JSON.parse(serviceAccountJson);
      } catch {
        console.warn('No service account found. Using default credentials.');
        // In production, you might want to use Application Default Credentials
        // This works when running on Google Cloud Platform
      }
    }

    const config: any = {
      databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
    };

    // Add credentials if service account is available
    if (serviceAccount) {
      config.credential = cert(serviceAccount);
    }

    return initializeApp(config);
  } catch (error) {
    console.error('Error initializing Firebase Admin:', error);
    throw error;
  }
}

// Lazy initialization to avoid build-time errors
let adminApp: any = null;
let adminDb: any = null;
let adminRealtimeDb: any = null;

export function getAdminApp() {
  if (!adminApp) {
    adminApp = initializeFirebaseAdmin();
  }
  return adminApp;
}

export function getAdminDb() {
  if (!adminDb) {
    adminDb = getFirestore(getAdminApp());
  }
  return adminDb;
}

export function getAdminRealtimeDb() {
  if (!adminRealtimeDb) {
    adminRealtimeDb = getDatabase(getAdminApp());
  }
  return adminRealtimeDb;
}

// For backward compatibility
export { getAdminDb as adminDb, getAdminRealtimeDb as adminRealtimeDb };

export default getAdminApp;
