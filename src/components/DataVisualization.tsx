'use client';

import { useMemo } from 'react';
import { <PERSON>Chart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';

interface DataVisualizationProps {
  realtimeData: any;
  firestoreData: { [collection: string]: any[] };
  loading: boolean;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export default function DataVisualization({ realtimeData, firestoreData, loading }: DataVisualizationProps) {
  const chartData = useMemo(() => {
    // Collection sizes for Firestore
    const collectionSizes = Object.entries(firestoreData).map(([name, docs]) => ({
      name,
      count: docs.length,
      type: 'Firestore'
    }));

    // Count items in realtime database
    const countRealtimeItems = (obj: any, path = ''): number => {
      if (!obj || typeof obj !== 'object') return 0;
      return Object.keys(obj).length + Object.values(obj).reduce((sum: number, value: any) => {
        if (typeof value === 'object' && value !== null) {
          return sum + countRealtimeItems(value);
        }
        return sum;
      }, 0);
    };

    const realtimeCount = countRealtimeItems(realtimeData);
    
    if (realtimeCount > 0) {
      collectionSizes.push({
        name: 'Realtime DB',
        count: realtimeCount,
        type: 'Realtime'
      });
    }

    return collectionSizes;
  }, [realtimeData, firestoreData]);

  const typeDistribution = useMemo(() => {
    const firestoreTotal = Object.values(firestoreData).reduce((sum, docs) => sum + docs.length, 0);
    const realtimeTotal = Object.keys(realtimeData).length;

    const distribution = [];
    if (firestoreTotal > 0) {
      distribution.push({ name: 'Firestore Documents', value: firestoreTotal });
    }
    if (realtimeTotal > 0) {
      distribution.push({ name: 'Realtime DB Nodes', value: realtimeTotal });
    }

    return distribution;
  }, [realtimeData, firestoreData]);

  // Sample time-series data (you can replace this with actual timestamp data from your database)
  const timeSeriesData = useMemo(() => {
    const now = new Date();
    const data = [];
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      // This is mock data - replace with actual data from your database
      data.push({
        date: date.toLocaleDateString(),
        firestore: Math.floor(Math.random() * 100) + 50,
        realtime: Math.floor(Math.random() * 50) + 20,
      });
    }
    
    return data;
  }, []);

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">Preparing visualizations...</p>
      </div>
    );
  }

  if (chartData.length === 0 && typeDistribution.length === 0) {
    return (
      <div className="p-8 text-center">
        <div className="text-gray-400 dark:text-gray-500 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No Data to Visualize
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Add some data to your Firebase databases to see visualizations here.
        </p>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-8">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
        Data Visualizations
      </h2>

      {/* Collection/Database Sizes Bar Chart */}
      {chartData.length > 0 && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Collection & Database Sizes
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="name" 
                className="text-gray-600 dark:text-gray-400"
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                className="text-gray-600 dark:text-gray-400"
                tick={{ fontSize: 12 }}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgb(31 41 55)',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
              <Bar dataKey="count" fill="#3B82F6" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Data Type Distribution Pie Chart */}
        {typeDistribution.length > 0 && (
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Data Distribution
            </h3>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={typeDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {typeDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgb(31 41 55)',
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Sample Time Series Chart */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Activity Trend (Sample Data)
          </h3>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="date" 
                className="text-gray-600 dark:text-gray-400"
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                className="text-gray-600 dark:text-gray-400"
                tick={{ fontSize: 12 }}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgb(31 41 55)',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
              <Line 
                type="monotone" 
                dataKey="firestore" 
                stroke="#3B82F6" 
                strokeWidth={2}
                name="Firestore"
              />
              <Line 
                type="monotone" 
                dataKey="realtime" 
                stroke="#10B981" 
                strokeWidth={2}
                name="Realtime DB"
              />
            </LineChart>
          </ResponsiveContainer>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            * This is sample data. Replace with actual timestamp data from your database.
          </p>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {Object.keys(firestoreData).length}
          </div>
          <div className="text-sm text-blue-800 dark:text-blue-300">Firestore Collections</div>
        </div>
        
        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {Object.values(firestoreData).reduce((sum, docs) => sum + docs.length, 0)}
          </div>
          <div className="text-sm text-green-800 dark:text-green-300">Total Documents</div>
        </div>
        
        <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {Object.keys(realtimeData).length}
          </div>
          <div className="text-sm text-purple-800 dark:text-purple-300">Realtime DB Nodes</div>
        </div>
      </div>
    </div>
  );
}
