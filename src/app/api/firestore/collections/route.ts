import { NextResponse } from 'next/server';
import { getAdminDb } from '@/lib/firebase-admin';

export async function GET() {
  try {
    // Get all collections
    const adminDb = getAdminDb();
    const collections = await adminDb.listCollections();
    
    const collectionData = await Promise.all(
      collections.map(async (collection: any) => {
        try {
          // Get document count for each collection
          const snapshot = await collection.limit(1).get();
          const countSnapshot = await collection.count().get();
          
          return {
            id: collection.id,
            name: collection.id,
            path: collection.path,
            documentCount: countSnapshot.data().count
          };
        } catch (error) {
          console.warn(`Error getting info for collection ${collection.id}:`, error);
          return {
            id: collection.id,
            name: collection.id,
            path: collection.path,
            documentCount: 0
          };
        }
      })
    );

    return NextResponse.json({
      collections: collectionData,
      totalCollections: collectionData.length
    });

  } catch (error) {
    console.error('Error listing collections:', error);
    return NextResponse.json(
      { error: 'Failed to list collections', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
