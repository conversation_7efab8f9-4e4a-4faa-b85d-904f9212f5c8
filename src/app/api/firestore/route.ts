import { NextRequest, NextResponse } from 'next/server';
import { getAdminDb } from '@/lib/firebase-admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const collection = searchParams.get('collection');
    const limit = parseInt(searchParams.get('limit') || '100');

    if (!collection) {
      return NextResponse.json({ error: 'Collection parameter is required' }, { status: 400 });
    }

    // Get documents from the specified collection
    const adminDb = getAdminDb();
    const snapshot = await adminDb.collection(collection).limit(limit).get();
    
    const documents = snapshot.docs.map((doc: any) => ({
      id: doc.id,
      ...doc.data()
    }));

    return NextResponse.json({
      collection,
      count: documents.length,
      documents
    });

  } catch (error) {
    console.error('Error fetching Firestore data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch Firestore data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { collection, data } = body;

    if (!collection || !data) {
      return NextResponse.json({ error: 'Collection and data are required' }, { status: 400 });
    }

    // Add document to the collection
    const adminDb = getAdminDb();
    const docRef = await adminDb.collection(collection).add(data);

    return NextResponse.json({
      success: true,
      id: docRef.id,
      message: `Document added to ${collection}`
    });

  } catch (error) {
    console.error('Error adding document:', error);
    return NextResponse.json(
      { error: 'Failed to add document', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
