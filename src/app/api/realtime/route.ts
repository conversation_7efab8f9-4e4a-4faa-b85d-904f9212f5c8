import { NextRequest, NextResponse } from 'next/server';
import { getAdminRealtimeDb } from '@/lib/firebase-admin';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path') || '/';
    const shallow = searchParams.get('shallow') === 'true';

    // Get reference to the specified path
    const adminRealtimeDb = getAdminRealtimeDb();
    const ref = adminRealtimeDb.ref(path);
    
    // Get data with optional shallow query
    const snapshot = await ref.once('value', undefined, shallow);
    const data = snapshot.val();

    return NextResponse.json({
      path,
      data,
      exists: snapshot.exists(),
      hasChildren: snapshot.hasChildren(),
      numChildren: snapshot.numChildren()
    });

  } catch (error) {
    console.error('Error fetching Realtime Database data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch Realtime Database data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { path, data, method = 'set' } = body;

    if (!path || data === undefined) {
      return NextResponse.json({ error: 'Path and data are required' }, { status: 400 });
    }

    const adminRealtimeDb = getAdminRealtimeDb();
    const ref = adminRealtimeDb.ref(path);

    let result;
    switch (method) {
      case 'set':
        await ref.set(data);
        result = { message: `Data set at ${path}` };
        break;
      case 'update':
        await ref.update(data);
        result = { message: `Data updated at ${path}` };
        break;
      case 'push':
        const pushRef = await ref.push(data);
        result = { message: `Data pushed to ${path}`, key: pushRef.key };
        break;
      default:
        return NextResponse.json({ error: 'Invalid method. Use set, update, or push' }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Error writing to Realtime Database:', error);
    return NextResponse.json(
      { error: 'Failed to write to Realtime Database', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
