import { NextResponse } from 'next/server';
import { getAdminDb, getAdminRealtimeDb } from '@/lib/firebase-admin';

export async function GET() {
  const status = {
    timestamp: new Date().toISOString(),
    firebase: {
      admin: false,
      firestore: false,
      realtimeDb: false
    },
    googleCloud: {
      projectId: process.env.GOOGLE_CLOUD_PROJECT_ID || process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      serviceAccount: false
    },
    environment: {
      nodeEnv: process.env.NODE_ENV,
      hasServiceAccountKey: !!(process.env.FIREBASE_SERVICE_ACCOUNT_KEY || process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64)
    }
  };

  try {
    // Test Firebase Admin initialization
    status.firebase.admin = true;

    // Test Firestore connection
    try {
      const adminDb = getAdminDb();
      await adminDb.collection('_test_connection').limit(1).get();
      status.firebase.firestore = true;
    } catch (error) {
      console.warn('Firestore connection test failed:', error);
    }

    // Test Realtime Database connection
    try {
      const adminRealtimeDb = getAdminRealtimeDb();
      await adminRealtimeDb.ref('/.info/connected').once('value');
      status.firebase.realtimeDb = true;
    } catch (error) {
      console.warn('Realtime Database connection test failed:', error);
    }

    // Check service account
    status.googleCloud.serviceAccount = status.environment.hasServiceAccountKey;

  } catch (error) {
    console.error('Status check error:', error);
    return NextResponse.json({
      ...status,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }

  const allGood = status.firebase.admin && 
                  (status.firebase.firestore || status.firebase.realtimeDb);

  return NextResponse.json(status, { 
    status: allGood ? 200 : 503 
  });
}
